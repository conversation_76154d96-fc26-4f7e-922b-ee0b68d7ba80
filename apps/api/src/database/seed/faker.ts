const names = [
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON>',
];

const rand = (max: number) => Math.floor(Math.random() * max);

const choose = <T>(arr: T[]): T => arr[rand(arr.length)]!;

export const randomName = () => choose(names);

export const randomEmail = (name: string) => {
  return name.toLowerCase().replace(/\s/g, '') + '@gmail.com';
};

export const randomPhone = (): string => {
  const subscriber = String(rand(10_000_000)).padStart(7, '0');
  return `+252 63 ${subscriber}`;
};
