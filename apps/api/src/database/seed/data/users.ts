import { hashPassword } from '@/core/password';
import type { Faker } from '@faker-js/faker';
import type { CreateUser } from '@muraadso/types/users';
import { emailFromName, randomSomaliMobile, randomSomaliName } from './somali';

export const getUsersData = (): CreateUser[] => {
  const sysopCount = 5;
  const customerUserCount = 20;
  const total = sysopCount + customerUserCount;

  const commonPassword = 'password123';
  const passwordHash = hashPassword(commonPassword);

  const usedEmails = new Set<string>();
  const usedPhones = new Set<string>();

  return Array.from({ length: total }).map((_, i) => {
    const name = randomSomaliName();

    let email = '';
    do {
      email = emailFromName(name.first, name.last);
    } while (usedEmails.has(email));
    usedEmails.add(email);

    let phone = '';
    do {
      phone = randomSomaliMobile();
    } while (usedPhones.has(phone));
    usedPhones.add(phone);

    const isSysop = i < sysopCount;
    return {
      email,
      phone,
      password: passwordHash,
      isSysop,
      isBanned: false,
    } satisfies CreateUser;
  });
};
