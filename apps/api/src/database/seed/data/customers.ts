import type { CreateCustomer } from '@muraadso/types/customers';
import type { User } from '@muraadso/types/users';
import { randomEmail, randomName, randomPhone } from '../faker';

export const getCustomersData = (users: User[]) => {
  const customerUsers = users.filter((u) => !u.isSysop);
  const toInsert: CreateCustomer[] = customerUsers.map((u) => {
    const name = randomName();
    const phone = randomPhone();
    const email = randomEmail(name);
    return {
      userId: u.id,
      name,
      phone,
      email,
      nationalId: '',
    } satisfies CreateCustomer;
  });
  return toInsert;
};
