import { type DB, createDb } from '@/database';
import * as s from '../schema';
import * as d from './data';

import 'dotenv/config';
import { sql } from 'drizzle-orm';
import { PgTable, getTableConfig } from 'drizzle-orm/pg-core';

(async () => {
  const NODE_ENV = process.env.NODE_ENV || 'development';
  if (NODE_ENV === 'production') return process.exit(0);

  const url = process.env.DATABASE_URL;
  const db = createDb(url!, NODE_ENV);

  await db.transaction(async (tx) => {
    console.log('Truncating existing tables...');
    for (const table of Object.values(s)) {
      if (table instanceof PgTable) {
        const config = getTableConfig(table);
        config.schema = config.schema === undefined ? 'public' : config.schema;
        const tablesToTruncate = [`"${config.schema}"."${config.name}"`];
        await (tx as unknown as DB).execute(
          sql.raw(`truncate ${tablesToTruncate.join(',')} cascade;`),
        );
      }
    }

    console.log('Now seeding data...');

    const users = await (tx as unknown as DB)
      .insert(s.users)
      .values(d.getUsersData())
      .returning();

    const [sysops, customers] = await Promise.all([
      await tx.insert(s.sysops).values(d.getSysopsData(users)).returning(),
      await tx
        .insert(s.customers)
        .values(d.getCustomersData(users))
        .returning(),
    ]);
  });

  console.log('Seeding done ✅!');
  process.exit(0);
})().catch((err) => {
  console.error(err);
  process.exit(1);
});
