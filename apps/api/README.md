# Muraadso API

## Setup

1. Copy the environment variables:
   ```bash
   cp .env.example .env
   ```

2. Fill in your database URL and other environment variables in `.env`:
   ```
   NODE_ENV=development
   DATABASE_URL=postgresql://postgres@localhost:6621/postgres
   ```

## Database

### Migrations

Run database migrations locally:
```bash
pnpm --filter api db:migrate:local
```

Run database migrations for staging (uses .env.staging):
```bash
pnpm --filter api db:migrate:staging
```

### Seeding

Seed the database with sample data (development/staging only; production is skipped):

Local:
```bash
pnpm --filter api db:seed:local
```

Staging (uses .env.staging):
```bash
pnpm --filter api db:seed:staging
```

## Development

Start the development server:
```bash
pnpm --filter api dev
```

The API will be available at `http://localhost:9921`

## Testing

Run tests:
```bash
pnpm --filter api test
```
